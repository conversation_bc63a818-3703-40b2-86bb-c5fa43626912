# دليل إعداد Supabase لوكالة تأجير السيارات
# Supabase Setup Guide for Car Rental Agency

## 📋 المتطلبات الأساسية
- حساب Supabase (مجاني أو مدفوع)
- مشروع Supabase جديد
- الوصول إلى SQL Editor في Supabase

## 🚀 خطوات الإعداد

### 1. إنشاء مشروع Supabase جديد
1. اذهب إلى [supabase.com](https://supabase.com)
2. قم بتسجيل الدخول أو إنشاء حساب جديد
3. انقر على "New Project"
4. اختر Organization
5. أدخل اسم المشروع: `click-car-rental`
6. اختر كلمة مرور قوية لقاعدة البيانات
7. اختر المنطقة الأقرب لك
8. انقر على "Create new project"

### 2. تشغيل ملف قاعدة البيانات الرئيسي
1. انتظر حتى يكتمل إعداد المشروع
2. اذهب إلى "SQL Editor" في الشريط الجانبي
3. انقر على "New query"
4. انسخ محتوى ملف `supabase-database-schema.sql` بالكامل
5. الصق المحتوى في محرر SQL
6. انقر على "Run" لتنفيذ الاستعلام
7. تأكد من ظهور رسائل النجاح

### 3. إنشاء أول مستخدم
1. اذهب إلى "Authentication" في الشريط الجانبي
2. انقر على "Add user"
3. أدخل البريد الإلكتروني وكلمة المرور
4. انقر على "Create user"
5. انسخ User ID (UUID) من جدول المستخدمين

### 4. إدراج البيانات التجريبية (اختياري)
1. ارجع إلى "SQL Editor"
2. انقر على "New query"
3. انسخ محتوى ملف `supabase-sample-data.sql`
4. استبدل جميع `'YOUR_USER_ID'` بـ UUID المستخدم الذي نسخته
5. الصق المحتوى في محرر SQL
6. انقر على "Run" لتنفيذ الاستعلام

### 5. إعداد Storage
1. اذهب إلى "Storage" في الشريط الجانبي
2. تأكد من إنشاء Buckets التالية:
   - `car-images` (public)
   - `client-documents` (private)
   - `agency-files` (public)

### 6. الحصول على مفاتيح API
1. اذهب إلى "Settings" > "API"
2. انسخ المعلومات التالية:
   - Project URL
   - anon public key
   - service_role key (للاستخدام الخادم فقط)

## 🔧 إعداد متغيرات البيئة

أنشئ ملف `.env.local` في مجلد المشروع وأضف:

```env
VITE_SUPABASE_URL=your_project_url_here
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

## 📊 هيكل قاعدة البيانات

### الجداول الرئيسية:
- **clients** - معلومات العملاء
- **cars** - معلومات السيارات
- **contracts** - العقود
- **reservations** - الحجوزات
- **finances** - المعاملات المالية
- **insurance** - التأمينات
- **maintenance** - الصيانة
- **customer_ratings** - تقييمات العملاء
- **taxes** - الضرائب والرسوم
- **agency_profiles** - ملفات الوكالات
- **images** - الصور والملفات
- **settings** - الإعدادات
- **audit_logs** - سجل العمليات

### الميزات المفعلة:
- ✅ Row Level Security (RLS)
- ✅ Real-time Subscriptions
- ✅ Storage Buckets
- ✅ Auto-generated UUIDs
- ✅ Timestamps
- ✅ Triggers
- ✅ Helper Functions

## 🔐 الأمان

### Row Level Security (RLS):
- كل مستخدم يمكنه الوصول فقط لبياناته الخاصة
- السياسات مطبقة على جميع الجداول
- حماية تلقائية ضد الوصول غير المصرح

### Storage Security:
- الصور العامة للسيارات والوكالة
- الوثائق الخاصة للعملاء محمية
- سياسات وصول مخصصة لكل bucket

## 🧪 اختبار الإعداد

### 1. اختبار الاتصال:
```javascript
import { supabase } from './lib/supabase'

// اختبار الاتصال
const testConnection = async () => {
  const { data, error } = await supabase.from('clients').select('count')
  if (error) {
    console.error('خطأ في الاتصال:', error)
  } else {
    console.log('الاتصال ناجح!')
  }
}
```

### 2. اختبار المصادقة:
```javascript
// اختبار تسجيل الدخول
const testAuth = async () => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'your-password'
  })
  
  if (error) {
    console.error('خطأ في المصادقة:', error)
  } else {
    console.log('تسجيل الدخول ناجح!', data)
  }
}
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في RLS**: تأكد من تسجيل الدخول قبل الوصول للبيانات
2. **خطأ في Storage**: تحقق من سياسات الوصول للـ buckets
3. **خطأ في الاتصال**: تأكد من صحة URL ومفاتيح API
4. **خطأ في البيانات**: تحقق من user_id في الجداول

### حلول:

```sql
-- التحقق من المستخدم الحالي
SELECT auth.uid();

-- التحقق من البيانات
SELECT * FROM clients WHERE user_id = auth.uid();

-- إعادة تعيين كلمة المرور
UPDATE auth.users SET encrypted_password = crypt('new_password', gen_salt('bf')) 
WHERE email = '<EMAIL>';
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من [وثائق Supabase](https://supabase.com/docs)
2. راجع [مجتمع Supabase](https://github.com/supabase/supabase/discussions)
3. تحقق من logs في Supabase Dashboard

## 🎉 تهانينا!

إذا اكتملت جميع الخطوات بنجاح، فإن قاعدة البيانات جاهزة للاستخدام مع تطبيق وكالة تأجير السيارات!

---

**ملاحظة مهمة**: احتفظ بنسخة احتياطية من مفاتيح API في مكان آمن ولا تشاركها مع أحد.
