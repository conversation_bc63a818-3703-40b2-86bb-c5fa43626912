-- ===================================================================
-- بيانات تجريبية لوكالة تأجير السيارات - Click Car
-- Sample Data for Car Rental Agency - Supabase
-- ===================================================================

-- ملاحظة: يجب تشغيل هذا الملف بعد إنشاء المستخدم الأول في Supabase Auth
-- Note: Run this file after creating the first user in Supabase Auth

-- ===================================================================
-- 1. بيانات تجريبية للعملاء (Sample Clients)
-- ===================================================================

-- استبدل 'YOUR_USER_ID' بـ UUID المستخدم الفعلي من auth.users
-- Replace 'YOUR_USER_ID' with actual user UUID from auth.users

INSERT INTO clients (user_id, full_name, phone, email, cin, city, address, nationality) VALUES
('YOUR_USER_ID', 'أحمد محمد الحسني', '+212 6 12 34 56 78', '<EMAIL>', 'AB123456', 'الدار البيضاء', 'حي المعاريف، شارع الحسن الثاني رقم 123', 'مغربي'),
('YOUR_USER_ID', 'فاطمة الزهراء', '+212 6 87 65 43 21', '<EMAIL>', 'CD789012', 'الرباط', 'حي أكدال، شارع محمد الخامس رقم 456', 'مغربي'),
('YOUR_USER_ID', 'يوسف بن علي', '+212 6 55 44 33 22', '<EMAIL>', 'EF345678', 'فاس', 'المدينة القديمة، درب الكبير رقم 789', 'مغربي'),
('YOUR_USER_ID', 'خديجة السعدي', '+212 6 99 88 77 66', '<EMAIL>', 'GH901234', 'مراكش', 'جليز، شارع الحرية رقم 321', 'مغربي'),
('YOUR_USER_ID', 'محمد الإدريسي', '+212 6 11 22 33 44', '<EMAIL>', 'IJ567890', 'طنجة', 'حي كاليفورنيا، شارع الاستقلال رقم 654', 'مغربي');

-- ===================================================================
-- 2. بيانات تجريبية للسيارات (Sample Cars)
-- ===================================================================

INSERT INTO cars (user_id, brand, model, year, license_plate, color, fuel_type, transmission, seats, mileage, daily_rate, status, technical_inspection_date, insurance_expiry_date, tax_expiry_date, description, features) VALUES
('YOUR_USER_ID', 'تويوتا', 'كامري', 2022, '12345-أ-67', 'أبيض', 'بنزين', 'أوتوماتيك', 5, 25000, 350.00, 'متاح', '2024-12-31', '2024-11-30', '2024-10-31', 'سيارة اقتصادية ومريحة للعائلات', ARRAY['مكيف', 'GPS', 'بلوتوث', 'كاميرا خلفية']),
('YOUR_USER_ID', 'هيونداي', 'إلنترا', 2021, '23456-ب-78', 'أسود', 'بنزين', 'يدوي', 5, 35000, 280.00, 'متاح', '2024-11-30', '2024-10-31', '2024-09-30', 'سيارة عملية واقتصادية', ARRAY['مكيف', 'راديو', 'USB']),
('YOUR_USER_ID', 'نيسان', 'صني', 2020, '34567-ج-89', 'أحمر', 'بنزين', 'أوتوماتيك', 5, 45000, 250.00, 'مؤجر', '2024-10-31', '2024-09-30', '2024-08-31', 'سيارة اقتصادية مثالية للمدينة', ARRAY['مكيف', 'راديو']),
('YOUR_USER_ID', 'رينو', 'كليو', 2023, '45678-د-90', 'أزرق', 'بنزين', 'يدوي', 5, 15000, 300.00, 'متاح', '2025-01-31', '2024-12-31', '2024-11-30', 'سيارة حديثة وأنيقة', ARRAY['مكيف', 'GPS', 'بلوتوث', 'شاشة لمس']),
('YOUR_USER_ID', 'فولكس واجن', 'بولو', 2021, '56789-ه-01', 'رمادي', 'ديزل', 'أوتوماتيك', 5, 30000, 320.00, 'صيانة', '2024-09-30', '2024-08-31', '2024-07-31', 'سيارة أوروبية عالية الجودة', ARRAY['مكيف', 'GPS', 'بلوتوث', 'مقاعد جلدية']);

-- ===================================================================
-- 3. بيانات تجريبية للعقود (Sample Contracts)
-- ===================================================================

INSERT INTO contracts (user_id, client_id, car_id, contract_number, start_date, end_date, daily_rate, total_amount, deposit_amount, payment_method, payment_status, contract_status, terms_conditions, notes, pickup_location, return_location, mileage_start, fuel_level_start) VALUES
('YOUR_USER_ID', 
 (SELECT id FROM clients WHERE full_name = 'أحمد محمد الحسني' LIMIT 1),
 (SELECT id FROM cars WHERE license_plate = '12345-أ-67' LIMIT 1),
 'CNT-2024-001', '2024-01-15', '2024-01-20', 350.00, 1750.00, 500.00, 'نقدي', 'مدفوع', 'منتهي', 'الشروط والأحكام العامة للإيجار', 'عميل ممتاز', 'مكتب الوكالة', 'مكتب الوكالة', 25000, 'ممتلئ'),

('YOUR_USER_ID',
 (SELECT id FROM clients WHERE full_name = 'فاطمة الزهراء' LIMIT 1),
 (SELECT id FROM cars WHERE license_plate = '34567-ج-89' LIMIT 1),
 'CNT-2024-002', '2024-01-18', '2024-01-25', 250.00, 1750.00, 400.00, 'بطاقة', 'مدفوع جزئياً', 'نشط', 'الشروط والأحكام العامة للإيجار', 'إيجار أسبوعي', 'المطار', 'مكتب الوكالة', 45000, 'ممتلئ');

-- ===================================================================
-- 4. بيانات تجريبية للحجوزات (Sample Reservations)
-- ===================================================================

INSERT INTO reservations (user_id, client_id, car_id, reservation_date, start_date, end_date, status, daily_rate, deposit_paid, notes) VALUES
('YOUR_USER_ID',
 (SELECT id FROM clients WHERE full_name = 'يوسف بن علي' LIMIT 1),
 (SELECT id FROM cars WHERE license_plate = '23456-ب-78' LIMIT 1),
 '2024-01-22', '2024-01-25', '2024-01-30', 'مؤكد', 280.00, 300.00, 'حجز لرحلة عمل'),

('YOUR_USER_ID',
 (SELECT id FROM clients WHERE full_name = 'خديجة السعدي' LIMIT 1),
 (SELECT id FROM cars WHERE license_plate = '45678-د-90' LIMIT 1),
 '2024-01-23', '2024-02-01', '2024-02-05', 'قيد الانتظار', 300.00, 0.00, 'في انتظار تأكيد الدفع');

-- ===================================================================
-- 5. بيانات تجريبية للمالية (Sample Finances)
-- ===================================================================

INSERT INTO finances (user_id, type, category, amount, description, date, payment_method, reference_type, receipt_number, notes) VALUES
('YOUR_USER_ID', 'income', 'إيجار', 1750.00, 'إيجار سيارة تويوتا كامري - أحمد محمد', '2024-01-20', 'نقدي', 'contract', 'REC-001', 'دفع كامل'),
('YOUR_USER_ID', 'income', 'إيجار', 1350.00, 'إيجار سيارة نيسان صني - فاطمة الزهراء (جزئي)', '2024-01-18', 'بطاقة', 'contract', 'REC-002', 'دفع جزئي - متبقي 400 درهم'),
('YOUR_USER_ID', 'expense', 'صيانة', 800.00, 'صيانة دورية لسيارة فولكس واجن بولو', '2024-01-19', 'نقدي', 'maintenance', 'MAINT-001', 'تغيير زيت وفلاتر'),
('YOUR_USER_ID', 'expense', 'وقود', 450.00, 'تعبئة وقود للسيارات', '2024-01-21', 'نقدي', 'fuel', 'FUEL-001', 'وقود شهري'),
('YOUR_USER_ID', 'income', 'وديعة', 500.00, 'وديعة حجز من أحمد محمد', '2024-01-15', 'نقدي', 'deposit', 'DEP-001', 'وديعة مسترجعة');

-- ===================================================================
-- 6. بيانات تجريبية للتأمينات (Sample Insurance)
-- ===================================================================

INSERT INTO insurance (user_id, car_id, insurance_company, policy_number, coverage_type, start_date, end_date, premium_amount, deductible_amount, coverage_details, agent_name, agent_phone, status, notes) VALUES
('YOUR_USER_ID',
 (SELECT id FROM cars WHERE license_plate = '12345-أ-67' LIMIT 1),
 'شركة الأمان للتأمين', 'POL-2024-001', 'تأمين شامل', '2024-01-01', '2024-12-31', 2500.00, 500.00, 'تغطية شاملة ضد جميع المخاطر', 'محمد التأميني', '+212 5 22 33 44 55', 'نشط', 'تأمين ممتاز'),

('YOUR_USER_ID',
 (SELECT id FROM cars WHERE license_plate = '23456-ب-78' LIMIT 1),
 'شركة الحماية', 'POL-2024-002', 'ضد الغير', '2024-01-01', '2024-12-31', 1200.00, 0.00, 'تأمين ضد الغير فقط', 'فاطمة التأمين', '+212 5 33 44 55 66', 'نشط', 'تأمين أساسي');

-- ===================================================================
-- 7. بيانات تجريبية للصيانة (Sample Maintenance)
-- ===================================================================

INSERT INTO maintenance (user_id, car_id, maintenance_type, description, cost, maintenance_date, next_maintenance_date, mileage_at_maintenance, service_provider, service_provider_phone, warranty_period, status, notes) VALUES
('YOUR_USER_ID',
 (SELECT id FROM cars WHERE license_plate = '56789-ه-01' LIMIT 1),
 'دورية', 'صيانة دورية - تغيير زيت وفلاتر', 800.00, '2024-01-19', '2024-04-19', 30000, 'ورشة الخبراء', '+212 5 44 55 66 77', 90, 'مكتمل', 'صيانة ممتازة'),

('YOUR_USER_ID',
 (SELECT id FROM cars WHERE license_plate = '12345-أ-67' LIMIT 1),
 'طارئة', 'إصلاح مكيف الهواء', 1200.00, '2024-01-10', NULL, 25000, 'مركز التبريد', '+212 5 55 66 77 88', 30, 'مكتمل', 'تم إصلاح المكيف بنجاح');

-- ===================================================================
-- 8. بيانات تجريبية لتقييم العملاء (Sample Customer Ratings)
-- ===================================================================

INSERT INTO customer_ratings (user_id, client_id, contract_id, rating, service_quality, car_condition, staff_behavior, overall_experience, comments, would_recommend, rating_date) VALUES
('YOUR_USER_ID',
 (SELECT id FROM clients WHERE full_name = 'أحمد محمد الحسني' LIMIT 1),
 (SELECT id FROM contracts WHERE contract_number = 'CNT-2024-001' LIMIT 1),
 5, 5, 5, 5, 5, 'خدمة ممتازة وسيارة نظيفة. أنصح بالتعامل مع هذه الوكالة.', true, '2024-01-21');

-- ===================================================================
-- 9. بيانات تجريبية للضرائب (Sample Taxes)
-- ===================================================================

INSERT INTO taxes (user_id, car_id, tax_type, tax_amount, due_date, payment_status, tax_year, reference_number, notes) VALUES
('YOUR_USER_ID',
 (SELECT id FROM cars WHERE license_plate = '12345-أ-67' LIMIT 1),
 'ضريبة الطريق', 1500.00, '2024-12-31', 'مدفوع', 2024, 'TAX-2024-001', 'مدفوع في الوقت المحدد'),

('YOUR_USER_ID',
 (SELECT id FROM cars WHERE license_plate = '23456-ب-78' LIMIT 1),
 'رسوم التسجيل', 800.00, '2024-11-30', 'غير مدفوع', 2024, 'TAX-2024-002', 'مستحق الدفع قريباً');

-- ===================================================================
-- 10. ملف الوكالة (Agency Profile)
-- ===================================================================

INSERT INTO agency_profiles (user_id, agency_name, license_number, phone, email, address, city, website, description, business_hours, social_media, bank_details, tax_number) VALUES
('YOUR_USER_ID', 'وكالة كليك للسيارات', 'LIC-2024-001', '+212 5 22 33 44 55', '<EMAIL>', 'شارع الحسن الثاني، الدار البيضاء', 'الدار البيضاء', 'www.clickvoiture.ma', 'وكالة متخصصة في تأجير السيارات بأفضل الأسعار وأعلى جودة خدمة',
'{"monday": "08:00-18:00", "tuesday": "08:00-18:00", "wednesday": "08:00-18:00", "thursday": "08:00-18:00", "friday": "08:00-18:00", "saturday": "09:00-17:00", "sunday": "closed"}',
'{"facebook": "https://facebook.com/clickvoiture", "instagram": "https://instagram.com/clickvoiture", "whatsapp": "+************"}',
'{"bank_name": "بنك المغرب", "account_number": "*********", "rib": "*********0*********01234"}',
'TAX-*********');

-- ===================================================================
-- رسالة النجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 تم إدراج البيانات التجريبية بنجاح!';
    RAISE NOTICE '📊 البيانات المدرجة:';
    RAISE NOTICE '   ✅ 5 عملاء';
    RAISE NOTICE '   ✅ 5 سيارات';
    RAISE NOTICE '   ✅ 2 عقود';
    RAISE NOTICE '   ✅ 2 حجوزات';
    RAISE NOTICE '   ✅ 5 معاملات مالية';
    RAISE NOTICE '   ✅ 2 تأمينات';
    RAISE NOTICE '   ✅ 2 صيانات';
    RAISE NOTICE '   ✅ 1 تقييم عميل';
    RAISE NOTICE '   ✅ 2 ضرائب';
    RAISE NOTICE '   ✅ 1 ملف وكالة';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  تذكر: استبدل YOUR_USER_ID بـ UUID المستخدم الفعلي';
    RAISE NOTICE '🚀 البيانات التجريبية جاهزة للاستخدام!';
END $$;
