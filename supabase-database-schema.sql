-- ===================================================================
-- قاعدة بيانات وكالة تأجير السيارات - Click Car
-- Supabase Database Schema for Car Rental Agency
-- ===================================================================

-- تفعيل Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- ===================================================================
-- 1. جدول العملاء (Clients)
-- ===================================================================

CREATE TABLE IF NOT EXISTS clients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    cin TEXT, -- رقم البطاقة الوطنية
    city TEXT,
    address TEXT,
    date_of_birth DATE,
    nationality TEXT DEFAULT 'مغربي',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 2. جدول السيارات (Cars)
-- ===================================================================

CREATE TABLE IF NOT EXISTS cars (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    brand TEXT NOT NULL,
    model TEXT NOT NULL,
    year INTEGER NOT NULL,
    license_plate TEXT UNIQUE NOT NULL,
    color TEXT,
    fuel_type TEXT DEFAULT 'بنزين', -- بنزين، ديزل، هجين، كهربائي
    transmission TEXT DEFAULT 'يدوي', -- يدوي، أوتوماتيك
    seats INTEGER DEFAULT 5,
    mileage INTEGER DEFAULT 0,
    daily_rate DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'متاح', -- متاح، مؤجر، صيانة، غير متاح
    technical_inspection_date DATE,
    insurance_expiry_date DATE,
    tax_expiry_date DATE,
    description TEXT,
    features TEXT[], -- مكيف، GPS، بلوتوث، إلخ
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 3. جدول العقود (Contracts)
-- ===================================================================

CREATE TABLE IF NOT EXISTS contracts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    car_id UUID REFERENCES cars(id) ON DELETE CASCADE,
    contract_number TEXT UNIQUE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    daily_rate DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    deposit_amount DECIMAL(10,2) DEFAULT 0,
    payment_method TEXT DEFAULT 'نقدي', -- نقدي، بطاقة، تحويل
    payment_status TEXT DEFAULT 'مدفوع جزئياً', -- مدفوع، مدفوع جزئياً، غير مدفوع
    contract_status TEXT DEFAULT 'نشط', -- نشط، منتهي، ملغي
    terms_conditions TEXT,
    notes TEXT,
    pickup_location TEXT,
    return_location TEXT,
    mileage_start INTEGER DEFAULT 0,
    mileage_end INTEGER,
    fuel_level_start TEXT DEFAULT 'ممتلئ',
    fuel_level_end TEXT,
    damage_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 4. جدول الحجوزات (Reservations)
-- ===================================================================

CREATE TABLE IF NOT EXISTS reservations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    car_id UUID REFERENCES cars(id) ON DELETE CASCADE,
    contract_id UUID REFERENCES contracts(id) ON DELETE SET NULL,
    reservation_date DATE NOT NULL DEFAULT CURRENT_DATE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status TEXT DEFAULT 'مؤكد', -- مؤكد، ملغي، مكتمل، قيد الانتظار
    total_days INTEGER GENERATED ALWAYS AS (end_date - start_date + 1) STORED,
    daily_rate DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) GENERATED ALWAYS AS (daily_rate * (end_date - start_date + 1)) STORED,
    deposit_paid DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 5. جدول المالية (Finances)
-- ===================================================================

CREATE TABLE IF NOT EXISTS finances (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL, -- income، expense
    category TEXT NOT NULL, -- إيجار، صيانة، وقود، تأمين، ضرائب، إلخ
    amount DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method TEXT DEFAULT 'نقدي', -- نقدي، بطاقة، تحويل، شيك
    reference_id UUID, -- ربط مع عقد أو فاتورة
    reference_type TEXT, -- contract، invoice، maintenance، إلخ
    receipt_number TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 6. جدول التأمينات (Insurance)
-- ===================================================================

CREATE TABLE IF NOT EXISTS insurance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    car_id UUID REFERENCES cars(id) ON DELETE CASCADE,
    insurance_company TEXT NOT NULL,
    policy_number TEXT NOT NULL,
    coverage_type TEXT NOT NULL, -- شامل، ضد الغير، إلخ
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    premium_amount DECIMAL(10,2) NOT NULL,
    deductible_amount DECIMAL(10,2) DEFAULT 0,
    coverage_details TEXT,
    agent_name TEXT,
    agent_phone TEXT,
    status TEXT DEFAULT 'نشط', -- نشط، منتهي، ملغي
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 7. جدول الصور والملفات (Images)
-- ===================================================================

CREATE TABLE IF NOT EXISTS images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    entity_type TEXT NOT NULL, -- car، client، manager
    entity_id UUID NOT NULL,
    image_type TEXT NOT NULL, -- main، interior، exterior، engine، id_front، id_back، passport، profile، license
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    width INTEGER,
    height INTEGER,
    is_primary BOOLEAN DEFAULT FALSE,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    uploaded_by UUID REFERENCES auth.users(id)
);

-- ===================================================================
-- 8. جدول ملفات الوكالات (Agency Profiles)
-- ===================================================================

CREATE TABLE IF NOT EXISTS agency_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    agency_name TEXT NOT NULL,
    license_number TEXT,
    phone TEXT,
    email TEXT,
    address TEXT,
    city TEXT,
    website TEXT,
    description TEXT,
    logo_url TEXT,
    business_hours JSONB, -- ساعات العمل
    social_media JSONB, -- روابط وسائل التواصل
    bank_details JSONB, -- تفاصيل البنك
    tax_number TEXT, -- رقم الضريبة
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 9. جدول سجل العمليات (Audit Logs)
-- ===================================================================

CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL, -- LOGIN، LOGOUT، CREATE، UPDATE، DELETE، FILE_UPLOAD، إلخ
    table_name TEXT,
    record_id TEXT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 10. جدول الصيانة (Maintenance)
-- ===================================================================

CREATE TABLE IF NOT EXISTS maintenance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    car_id UUID REFERENCES cars(id) ON DELETE CASCADE,
    maintenance_type TEXT NOT NULL, -- دورية، طارئة، إصلاح
    description TEXT NOT NULL,
    cost DECIMAL(10,2) NOT NULL,
    maintenance_date DATE NOT NULL DEFAULT CURRENT_DATE,
    next_maintenance_date DATE,
    mileage_at_maintenance INTEGER,
    service_provider TEXT, -- اسم الورشة أو مقدم الخدمة
    service_provider_phone TEXT,
    warranty_period INTEGER, -- بالأيام
    status TEXT DEFAULT 'مكتمل', -- مكتمل، قيد التنفيذ، مجدول
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 11. جدول تقييم العملاء (Customer Ratings)
-- ===================================================================

CREATE TABLE IF NOT EXISTS customer_ratings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    contract_id UUID REFERENCES contracts(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    service_quality INTEGER CHECK (service_quality >= 1 AND service_quality <= 5),
    car_condition INTEGER CHECK (car_condition >= 1 AND car_condition <= 5),
    staff_behavior INTEGER CHECK (staff_behavior >= 1 AND staff_behavior <= 5),
    overall_experience INTEGER CHECK (overall_experience >= 1 AND overall_experience <= 5),
    comments TEXT,
    would_recommend BOOLEAN DEFAULT TRUE,
    rating_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- 12. جدول الإعدادات (Settings)
-- ===================================================================

CREATE TABLE IF NOT EXISTS settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    setting_key TEXT NOT NULL,
    setting_value JSONB NOT NULL,
    setting_type TEXT DEFAULT 'user', -- user، system، agency
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, setting_key)
);

-- ===================================================================
-- 13. جدول الضرائب (Taxes)
-- ===================================================================

CREATE TABLE IF NOT EXISTS taxes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    car_id UUID REFERENCES cars(id) ON DELETE CASCADE,
    tax_type TEXT NOT NULL, -- رسوم التسجيل، ضريبة الطريق، إلخ
    tax_amount DECIMAL(10,2) NOT NULL,
    due_date DATE NOT NULL,
    payment_date DATE,
    payment_status TEXT DEFAULT 'غير مدفوع', -- مدفوع، غير مدفوع، متأخر
    tax_year INTEGER,
    reference_number TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================================================
-- الفهارس (Indexes)
-- ===================================================================

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_clients_user_id ON clients(user_id);
CREATE INDEX IF NOT EXISTS idx_clients_phone ON clients(phone);
CREATE INDEX IF NOT EXISTS idx_clients_cin ON clients(cin);

CREATE INDEX IF NOT EXISTS idx_cars_user_id ON cars(user_id);
CREATE INDEX IF NOT EXISTS idx_cars_license_plate ON cars(license_plate);
CREATE INDEX IF NOT EXISTS idx_cars_status ON cars(status);
CREATE INDEX IF NOT EXISTS idx_cars_brand_model ON cars(brand, model);

CREATE INDEX IF NOT EXISTS idx_contracts_user_id ON contracts(user_id);
CREATE INDEX IF NOT EXISTS idx_contracts_client_id ON contracts(client_id);
CREATE INDEX IF NOT EXISTS idx_contracts_car_id ON contracts(car_id);
CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(contract_status);
CREATE INDEX IF NOT EXISTS idx_contracts_dates ON contracts(start_date, end_date);

CREATE INDEX IF NOT EXISTS idx_reservations_user_id ON reservations(user_id);
CREATE INDEX IF NOT EXISTS idx_reservations_dates ON reservations(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_reservations_status ON reservations(status);

CREATE INDEX IF NOT EXISTS idx_finances_user_id ON finances(user_id);
CREATE INDEX IF NOT EXISTS idx_finances_type ON finances(type);
CREATE INDEX IF NOT EXISTS idx_finances_date ON finances(date);
CREATE INDEX IF NOT EXISTS idx_finances_category ON finances(category);

CREATE INDEX IF NOT EXISTS idx_insurance_user_id ON insurance(user_id);
CREATE INDEX IF NOT EXISTS idx_insurance_car_id ON insurance(car_id);
CREATE INDEX IF NOT EXISTS idx_insurance_dates ON insurance(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_insurance_status ON insurance(status);

CREATE INDEX IF NOT EXISTS idx_images_entity ON images(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_images_user_id ON images(user_id);

CREATE INDEX IF NOT EXISTS idx_maintenance_user_id ON maintenance(user_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_car_id ON maintenance(car_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_date ON maintenance(maintenance_date);

CREATE INDEX IF NOT EXISTS idx_customer_ratings_user_id ON customer_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_customer_ratings_client_id ON customer_ratings(client_id);

CREATE INDEX IF NOT EXISTS idx_taxes_user_id ON taxes(user_id);
CREATE INDEX IF NOT EXISTS idx_taxes_car_id ON taxes(car_id);
CREATE INDEX IF NOT EXISTS idx_taxes_due_date ON taxes(due_date);

-- ===================================================================
-- تفعيل Row Level Security (RLS)
-- ===================================================================

-- تفعيل RLS على جميع الجداول
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE reservations ENABLE ROW LEVEL SECURITY;
ALTER TABLE finances ENABLE ROW LEVEL SECURITY;
ALTER TABLE insurance ENABLE ROW LEVEL SECURITY;
ALTER TABLE images ENABLE ROW LEVEL SECURITY;
ALTER TABLE agency_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE taxes ENABLE ROW LEVEL SECURITY;

-- ===================================================================
-- سياسات الأمان (RLS Policies)
-- ===================================================================

-- سياسات جدول العملاء
CREATE POLICY "Users can view their own clients" ON clients
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own clients" ON clients
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own clients" ON clients
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own clients" ON clients
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول السيارات
CREATE POLICY "Users can view their own cars" ON cars
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own cars" ON cars
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own cars" ON cars
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own cars" ON cars
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول العقود
CREATE POLICY "Users can view their own contracts" ON contracts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own contracts" ON contracts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own contracts" ON contracts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own contracts" ON contracts
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول الحجوزات
CREATE POLICY "Users can view their own reservations" ON reservations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own reservations" ON reservations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reservations" ON reservations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reservations" ON reservations
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول المالية
CREATE POLICY "Users can view their own finances" ON finances
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own finances" ON finances
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own finances" ON finances
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own finances" ON finances
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول التأمينات
CREATE POLICY "Users can view their own insurance" ON insurance
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own insurance" ON insurance
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own insurance" ON insurance
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own insurance" ON insurance
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول الصور
CREATE POLICY "Users can view their own images" ON images
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own images" ON images
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own images" ON images
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own images" ON images
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول ملفات الوكالات
CREATE POLICY "Users can view their own agency profile" ON agency_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own agency profile" ON agency_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own agency profile" ON agency_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- سياسات جدول سجل العمليات
CREATE POLICY "Users can view their own audit logs" ON audit_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own audit logs" ON audit_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- سياسات جدول الصيانة
CREATE POLICY "Users can view their own maintenance" ON maintenance
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own maintenance" ON maintenance
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own maintenance" ON maintenance
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own maintenance" ON maintenance
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول تقييم العملاء
CREATE POLICY "Users can view their own customer ratings" ON customer_ratings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own customer ratings" ON customer_ratings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own customer ratings" ON customer_ratings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own customer ratings" ON customer_ratings
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول الإعدادات
CREATE POLICY "Users can view their own settings" ON settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own settings" ON settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings" ON settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own settings" ON settings
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول الضرائب
CREATE POLICY "Users can view their own taxes" ON taxes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own taxes" ON taxes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own taxes" ON taxes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own taxes" ON taxes
    FOR DELETE USING (auth.uid() = user_id);

-- ===================================================================
-- الدوال المساعدة (Helper Functions)
-- ===================================================================

-- دالة لحساب إحصائيات الوكالة
CREATE OR REPLACE FUNCTION get_agency_stats(agency_user_id UUID)
RETURNS JSON AS $$
DECLARE
    total_cars INTEGER;
    available_cars INTEGER;
    rented_cars INTEGER;
    total_clients INTEGER;
    active_contracts INTEGER;
    monthly_revenue DECIMAL(10,2);
    weekly_revenue DECIMAL(10,2);
    pending_payments DECIMAL(10,2);
    result JSON;
BEGIN
    -- عدد السيارات
    SELECT COUNT(*) INTO total_cars FROM cars WHERE user_id = agency_user_id;
    SELECT COUNT(*) INTO available_cars FROM cars WHERE user_id = agency_user_id AND status = 'متاح';
    SELECT COUNT(*) INTO rented_cars FROM cars WHERE user_id = agency_user_id AND status = 'مؤجر';

    -- عدد العملاء
    SELECT COUNT(*) INTO total_clients FROM clients WHERE user_id = agency_user_id;

    -- العقود النشطة
    SELECT COUNT(*) INTO active_contracts FROM contracts
    WHERE user_id = agency_user_id AND contract_status = 'نشط';

    -- الإيرادات الشهرية
    SELECT COALESCE(SUM(amount), 0) INTO monthly_revenue
    FROM finances
    WHERE user_id = agency_user_id
    AND type = 'income'
    AND date >= DATE_TRUNC('month', CURRENT_DATE);

    -- الإيرادات الأسبوعية
    SELECT COALESCE(SUM(amount), 0) INTO weekly_revenue
    FROM finances
    WHERE user_id = agency_user_id
    AND type = 'income'
    AND date >= DATE_TRUNC('week', CURRENT_DATE);

    -- المدفوعات المعلقة
    SELECT COALESCE(SUM(total_amount - deposit_amount), 0) INTO pending_payments
    FROM contracts
    WHERE user_id = agency_user_id
    AND payment_status IN ('غير مدفوع', 'مدفوع جزئياً');

    -- بناء النتيجة
    result := json_build_object(
        'total_cars', total_cars,
        'available_cars', available_cars,
        'rented_cars', rented_cars,
        'total_clients', total_clients,
        'active_contracts', active_contracts,
        'monthly_revenue', monthly_revenue,
        'weekly_revenue', weekly_revenue,
        'pending_payments', pending_payments
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة لتحديث حالة السيارة تلقائياً
CREATE OR REPLACE FUNCTION update_car_status()
RETURNS TRIGGER AS $$
BEGIN
    -- عند إنشاء عقد جديد، تحديث حالة السيارة إلى "مؤجر"
    IF TG_OP = 'INSERT' AND NEW.contract_status = 'نشط' THEN
        UPDATE cars SET status = 'مؤجر' WHERE id = NEW.car_id;
    END IF;

    -- عند انتهاء العقد، تحديث حالة السيارة إلى "متاح"
    IF TG_OP = 'UPDATE' AND OLD.contract_status = 'نشط' AND NEW.contract_status IN ('منتهي', 'ملغي') THEN
        UPDATE cars SET status = 'متاح' WHERE id = NEW.car_id;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفز
CREATE TRIGGER trigger_update_car_status
    AFTER INSERT OR UPDATE ON contracts
    FOR EACH ROW
    EXECUTE FUNCTION update_car_status();

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفزات لتحديث updated_at
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cars_updated_at BEFORE UPDATE ON cars
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contracts_updated_at BEFORE UPDATE ON contracts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reservations_updated_at BEFORE UPDATE ON reservations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_finances_updated_at BEFORE UPDATE ON finances
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_insurance_updated_at BEFORE UPDATE ON insurance
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agency_profiles_updated_at BEFORE UPDATE ON agency_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_updated_at BEFORE UPDATE ON maintenance
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_taxes_updated_at BEFORE UPDATE ON taxes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===================================================================
-- إعداد Storage للملفات والصور
-- ===================================================================

-- إنشاء bucket للصور
INSERT INTO storage.buckets (id, name, public)
VALUES ('car-images', 'car-images', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('client-documents', 'client-documents', false)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('agency-files', 'agency-files', true)
ON CONFLICT (id) DO NOTHING;

-- سياسات Storage
CREATE POLICY "Users can upload car images" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'car-images' AND auth.role() = 'authenticated');

CREATE POLICY "Users can view car images" ON storage.objects
    FOR SELECT USING (bucket_id = 'car-images');

CREATE POLICY "Users can update their car images" ON storage.objects
    FOR UPDATE USING (bucket_id = 'car-images' AND auth.role() = 'authenticated');

CREATE POLICY "Users can delete their car images" ON storage.objects
    FOR DELETE USING (bucket_id = 'car-images' AND auth.role() = 'authenticated');

CREATE POLICY "Users can upload client documents" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'client-documents' AND auth.role() = 'authenticated');

CREATE POLICY "Users can view their client documents" ON storage.objects
    FOR SELECT USING (bucket_id = 'client-documents' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update their client documents" ON storage.objects
    FOR UPDATE USING (bucket_id = 'client-documents' AND auth.role() = 'authenticated');

CREATE POLICY "Users can delete their client documents" ON storage.objects
    FOR DELETE USING (bucket_id = 'client-documents' AND auth.role() = 'authenticated');

CREATE POLICY "Users can upload agency files" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'agency-files' AND auth.role() = 'authenticated');

CREATE POLICY "Users can view agency files" ON storage.objects
    FOR SELECT USING (bucket_id = 'agency-files');

CREATE POLICY "Users can update their agency files" ON storage.objects
    FOR UPDATE USING (bucket_id = 'agency-files' AND auth.role() = 'authenticated');

CREATE POLICY "Users can delete their agency files" ON storage.objects
    FOR DELETE USING (bucket_id = 'agency-files' AND auth.role() = 'authenticated');

-- ===================================================================
-- إعداد Real-time Subscriptions
-- ===================================================================

-- تفعيل Real-time للجداول المهمة
ALTER PUBLICATION supabase_realtime ADD TABLE clients;
ALTER PUBLICATION supabase_realtime ADD TABLE cars;
ALTER PUBLICATION supabase_realtime ADD TABLE contracts;
ALTER PUBLICATION supabase_realtime ADD TABLE reservations;
ALTER PUBLICATION supabase_realtime ADD TABLE finances;

-- ===================================================================
-- رسالة النجاح
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 تم إنشاء قاعدة البيانات بنجاح!';
    RAISE NOTICE '📊 الجداول المنشأة:';
    RAISE NOTICE '   ✅ clients - العملاء';
    RAISE NOTICE '   ✅ cars - السيارات';
    RAISE NOTICE '   ✅ contracts - العقود';
    RAISE NOTICE '   ✅ reservations - الحجوزات';
    RAISE NOTICE '   ✅ finances - المالية';
    RAISE NOTICE '   ✅ insurance - التأمينات';
    RAISE NOTICE '   ✅ images - الصور والملفات';
    RAISE NOTICE '   ✅ agency_profiles - ملفات الوكالات';
    RAISE NOTICE '   ✅ audit_logs - سجل العمليات';
    RAISE NOTICE '   ✅ maintenance - الصيانة';
    RAISE NOTICE '   ✅ customer_ratings - تقييم العملاء';
    RAISE NOTICE '   ✅ settings - الإعدادات';
    RAISE NOTICE '   ✅ taxes - الضرائب';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 الميزات المفعلة:';
    RAISE NOTICE '   ✅ Row Level Security (RLS)';
    RAISE NOTICE '   ✅ Storage Buckets';
    RAISE NOTICE '   ✅ Real-time Subscriptions';
    RAISE NOTICE '   ✅ Helper Functions';
    RAISE NOTICE '   ✅ Auto Triggers';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 قاعدة البيانات جاهزة للاستخدام مع Supabase!';
    RAISE NOTICE '📝 تذكر: قم بتحديث متغيرات البيئة في ملف .env';
END $$;
